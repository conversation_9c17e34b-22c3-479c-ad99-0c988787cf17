'use client';

import { useState } from 'react';
import { signInWithRedirect } from 'aws-amplify/auth';
import { useAuth } from './auth-provider';

export function GoogleSignInButton() {
  const [isLoading, setIsLoading] = useState(false);
  const { isAmplifyConfigured } = useAuth();

  // Check if OAuth environment variables are configured
  const isOAuthConfigured = process.env.NEXT_PUBLIC_QBRAID_COGNITO_DOMAIN || true;

  const handleGoogleSignIn = async () => {
    if (!isAmplifyConfigured) {
      console.error('Amplify not configured yet');
      return;
    }

    if (!isOAuthConfigured) {
      alert('Google OAuth is not configured. Please contact support.');
      return;
    }

    try {
      setIsLoading(true);
      console.log('🔐 [AUTH] Initiating Google OAuth sign-in...');

      // This will redirect to Google OAuth via Cognito hosted UI
      await signInWithRedirect({
        provider: 'Google',
        customState: JSON.stringify({
          returnUrl: '/',
        }),
      });
    } catch (error) {
      console.error('❌ [AUTH] Google sign-in error:', error);

      // // Show user-friendly error message
      // if (error instanceof Error && error.message.includes('oauth param not configured')) {
      //   alert('Google OAuth is not properly configured. Please contact support.');
      // } else {
      //   alert('Failed to sign in with Google. Please try again.');
      // }

      setIsLoading(false);
    }
  };

  // Show disabled state if OAuth is not configured
  const isDisabled = isLoading || !isAmplifyConfigured || !isOAuthConfigured;

  return (
    <button
      type="button"
      onClick={handleGoogleSignIn}
      disabled={isDisabled}
      className="flex items-center justify-center h-12 rounded-xl border border-slate-600/50 bg-slate-800/30 hover:bg-slate-700/50 transition-all duration-200 group disabled:opacity-50 disabled:cursor-not-allowed"
      title={!isOAuthConfigured ? 'Google OAuth not configured' : 'Sign in with Google'}
    >
      {isLoading ? (
        <div className="w-5 h-5 border-2 border-slate-400/30 border-t-slate-400 rounded-full animate-spin" />
      ) : (
        <>
          <img
            src="https://www.svgrepo.com/show/475656/google-color.svg"
            alt="Google"
            className="w-5 h-5 group-hover:scale-110 transition-transform"
          />
          <span className="text-sm font-medium text-slate-300 ml-2">
            {!isOAuthConfigured ? 'Google (Not Configured)' : 'Google'}
          </span>
        </>
      )}
    </button>
  );
}
